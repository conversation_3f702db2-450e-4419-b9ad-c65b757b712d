// SGA Official color palette
const THEME = {
  // SGA Green Color Palette
  SOFT_GREEN: '#A2D6A0',        // Soft Green: rgb(162, 214, 160)
  LIGHT_MINT: '#E5F2E5',        // Light Mint: rgb(229, 242, 229)
  FRESH_GREEN: '#8DCE8C',       // Fresh Green: rgb(141, 206, 140)
  PALE_GREEN: '#BAE0B7',        // Pale Green: rgb(186, 224, 183)
  VIBRANT_TEAL_GREEN: '#68C692', // Vibrant Teal Green: rgb(104, 198, 146)
  MISTY_GREEN: '#CEE8CD',       // Misty Green: rgb(206, 232, 205)

  // Primary colors for main UI elements
  PRIMARY: '#68C692',           // Vibrant Teal Green as primary
  PRIMARY_DARK: '#5AB080',      // Darker shade for hover states
  SECONDARY: '#A2D6A0',         // Soft Green as secondary

  // Background and text colors
  BACKGROUND_LIGHT: 'rgba(255, 255, 255, 0.9)',
  BACKGROUND_MINT: '#E5F2E5',
  TEXT_DARK: '#333333',
  TEXT_LIGHT: '#666666',

  // Status colors
  SUCCESS: '#68C692',
  ERROR_RED: '#e74c3c',
  WARNING: '#f39c12'
};

// Export to make accessible to other scripts
window.THEME = THEME;

document.addEventListener('DOMContentLoaded', function() {
  // Add responsive viewport meta tag if not already present
  if (!document.querySelector('meta[name="viewport"]')) {
    const metaViewport = document.createElement('meta');
    metaViewport.name = 'viewport';
    metaViewport.content = 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no';
    document.head.appendChild(metaViewport);
  }

  // Improve mobile interaction for role suggestions
  improveMobileRoleSuggestions();

  // Add touch-friendly behavior to buttons
  addTouchFriendlyBehavior();

  // Ensure quiz container remains scrollable on mobile
  improveQuizScrolling();
});

function improveMobileRoleSuggestions() {
  const roleInput = document.getElementById('role');
  if (!roleInput) return;

  // Create a better container for role suggestions on mobile
  const suggestionsContainer = document.querySelector('.role-suggestions');
  if (suggestionsContainer) {
    // Add a touch-friendly behavior for mobile
    suggestionsContainer.addEventListener('touchstart', function(e) {
      // Prevent immediate closing on mobile touch start
      e.stopPropagation();
    });

    // Close suggestions when tapping elsewhere on mobile
    document.addEventListener('touchstart', function(e) {
      if (suggestionsContainer.style.display !== 'none' && 
          !roleInput.contains(e.target) && 
          !suggestionsContainer.contains(e.target)) {
        suggestionsContainer.style.display = 'none';
      }
    });

    // Ensure that on mobile the suggestions don't get clipped
    window.addEventListener('resize', function() {
      if (window.innerWidth <= 768) {
        if (suggestionsContainer.style.display !== 'none') {
          // Calculate if we need to adjust position to remain visible
          const inputRect = roleInput.getBoundingClientRect();
          const availableHeight = window.innerHeight - inputRect.bottom - 20;
          
          if (availableHeight < 150) {
            suggestionsContainer.style.maxHeight = `${availableHeight}px`;
          } else {
            suggestionsContainer.style.maxHeight = '150px';
          }
        }
      }
    });
  }
}

function addTouchFriendlyBehavior() {
  // Make quiz buttons more responsive on touch
  const buttons = document.querySelectorAll('.option-btn, #next-btn, .skip-btn');
  
  buttons.forEach(button => {
    // Add active state for touch
    button.addEventListener('touchstart', function() {
      this.classList.add('touch-active');
    });
    
    button.addEventListener('touchend', function() {
      this.classList.remove('touch-active');
    });
  });
}

function improveQuizScrolling() {
  // Ensure quiz container is scrollable on mobile
  const quizContainer = document.getElementById('quiz-container');
  if (quizContainer) {
    quizContainer.style.overflowY = 'auto';
    quizContainer.style.webkitOverflowScrolling = 'touch';
    
    // Adjust height on mobile to ensure it fits in viewport
    if (window.innerWidth <= 768) {
      quizContainer.style.maxHeight = '80vh';
    }
  }
}
